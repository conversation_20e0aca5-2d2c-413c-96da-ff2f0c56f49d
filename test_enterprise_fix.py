#!/usr/bin/env python3
"""
Test script to verify the enterprise pipeline system fix for 'unhashable type: dict' error.
"""

import json
from datetime import datetime

def test_enterprise_pipeline_fix():
    """Test the specific fix for the unhashable type error."""
    
    print("🧪 Testing enterprise pipeline fix...")
    
    # Simulate the problematic data structures
    selected_components = {
        "pdf_parsing": "unstructured",
        "chunking": "recursive", 
        "embedding": "azure",
        "vector_store": "qdrant",
        "llm": "azure_openai",
        "tools": ["tavily"]
    }
    
    architecture = {
        "data": {
            "architecture_pattern": "microservices"
        },
        "quality_level": "SUPERIOR_ENTERPRISE_GRADE"
    }
    
    collaborative_result = {
        "collaborative_insights": ["insight1", "insight2"]
    }
    
    try:
        # Test the problematic f-string constructions that were causing the error
        
        # Test 1: Components in f-string (this was causing the error)
        test_string_1 = f"Components: {json.dumps(selected_components)}"
        print(f"✅ Test 1 passed: {test_string_1[:50]}...")
        
        # Test 2: Pipeline state dictionary construction
        pipeline_state = {
            "documents_processed": 0,
            "queries_processed": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "version": "2.0.0-SUPERIOR",
            "architecture": f"{architecture.get('data', {}).get('architecture_pattern', 'microservices')}",
            "components": f"{json.dumps(selected_components)}"
        }
        print(f"✅ Test 2 passed: Pipeline state created successfully")
        
        # Test 3: Main app code generation (simplified)
        main_app_code = f'''"""
SUPERIOR Enterprise Data Pipeline Application
Generated by Enhanced Enterprise AI Pipeline Generator with Collaborative Agents

Architecture Quality: {architecture.get("quality_level", "SUPERIOR_ENTERPRISE_GRADE")}
Components: {json.dumps(selected_components)}
Collaborative Insights: {len(collaborative_result.get("collaborative_insights", []))} agent contributions
"""
'''
        print(f"✅ Test 3 passed: Main app code generated successfully")
        
        # Test 4: Verify no unhashable type errors when using these in sets or dicts
        test_set = set([json.dumps(selected_components)])
        print(f"✅ Test 4 passed: Can create set with JSON string")
        
        print("\n🎉 All tests passed! The enterprise pipeline fix is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = test_enterprise_pipeline_fix()
    exit(0 if success else 1)
