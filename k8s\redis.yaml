apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: enterprise-pipeline
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: enterprise-pipeline
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
